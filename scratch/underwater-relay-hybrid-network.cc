/*
 * 水下-水面-水上混合网络仿真程序
 * 
 * 网络架构：
 * - 4个UAN节点（水下声学网络）
 * - 1个中继节点（水面，同时支持水下通信和WiFi）
 * - 1个船舶节点（水面）
 * - 3个UAV节点（空中）
 * 
 * 通信方式：
 * - UAN ↔ 中继：使用aqua-sim-ng的VBF路由协议
 * - 中继 ↔ 船舶/UAV：使用WiFi Ad-hoc模式
 * 
 * 作者：NS3仿真程序
 * 日期：2025-08-01
 */

#include <iostream>
#include <iomanip>
#include <fstream>
#include <vector>
#include <map>

#include "ns3/core-module.h"
#include "ns3/network-module.h"
#include "ns3/mobility-module.h"
#include "ns3/wifi-module.h"
#include "ns3/internet-module.h"
#include "ns3/applications-module.h"
#include "ns3/flow-monitor-module.h"
#include "ns3/aqua-sim-ng-module.h"
#include "ns3/aqua-sim-traffic-gen-helper.h"
#include "ns3/aqua-sim-application-helper.h"

using namespace ns3;

NS_LOG_COMPONENT_DEFINE("UnderwaterRelayHybridNetwork");

// 全局变量用于统计
std::map<uint32_t, uint32_t> g_txPackets;  // 发送的数据包数量
std::map<uint32_t, uint32_t> g_rxPackets;  // 接收的数据包数量
std::map<uint32_t, std::string> g_nodeTypes; // 节点类型

// 中继节点的全局引用
Ptr<Node> g_relayNode;
Ptr<NetDevice> g_relayUnderwaterDevice;
Ptr<NetDevice> g_relayWifiDevice;
Ipv4Address g_relayWifiAddress;
Ptr<Socket> g_relayWifiSocket; // 用于UAN到WiFi的转发

// 全局变量用于UAN数据包发送
NodeContainer g_uanNodes;
NetDeviceContainer g_underwaterDevices;

// 全局函数来模拟UAN数据包到达中继节点
void SimulateUanToRelayPacket(uint32_t fromNodeId, uint32_t packetSize)
{
    if (g_relayNode) {
        // 统计中继节点接收的数据包
        uint32_t relayNodeId = g_relayNode->GetId();
        g_rxPackets[relayNodeId]++;

        std::cout << "Time: " << Simulator::Now().GetSeconds()
                  << "s - Relay received underwater packet from UAN-" << fromNodeId
                  << " (Size: " << packetSize << " bytes), forwarding to WiFi network" << std::endl;

        // 真正转发到WiFi网络
        if (g_relayWifiSocket) {
            // 创建数据包
            Ptr<Packet> forwardPacket = Create<Packet>(packetSize);

            // 广播到WiFi网络
            InetSocketAddress broadcastAddr(Ipv4Address("**********"), 2000);
            int result = g_relayWifiSocket->SendTo(forwardPacket, 0, broadcastAddr);

            if (result > 0) {
                g_txPackets[relayNodeId]++;
                std::cout << "Time: " << Simulator::Now().GetSeconds()
                          << "s - Relay forwarded underwater packet to WiFi network (broadcast)" << std::endl;
            } else {
                std::cout << "Time: " << Simulator::Now().GetSeconds()
                          << "s - Relay failed to forward underwater packet to WiFi network" << std::endl;
            }
        } else {
            std::cout << "Time: " << Simulator::Now().GetSeconds()
                      << "s - Relay WiFi socket not available for forwarding" << std::endl;
        }
    }
}

// UAN节点发送数据包的函数
void SendUanPacket(uint32_t nodeId)
{
    std::cout << "Time: " << Simulator::Now().GetSeconds()
              << "s - SendUanPacket called for UAN-" << nodeId << std::endl;

    if (nodeId < g_uanNodes.GetN()) {
        Ptr<Node> node = g_uanNodes.Get(nodeId);
        Ptr<NetDevice> device = g_underwaterDevices.Get(nodeId);

        std::cout << "Time: " << Simulator::Now().GetSeconds()
                  << "s - UAN-" << nodeId << " attempting to send packet..." << std::endl;

        // 创建数据包
        Ptr<Packet> packet = Create<Packet>(100); // 100字节数据包

        // 模拟发送成功
        g_txPackets[nodeId]++;
        std::cout << "Time: " << Simulator::Now().GetSeconds()
                  << "s - UAN-" << nodeId << " sent packet #" << g_txPackets[nodeId]
                  << " (Size: 100 bytes) to relay" << std::endl;

        // 模拟数据包到达中继节点
        SimulateUanToRelayPacket(nodeId, 100);

        // 安排下一次发送
        double nextTime = 0.5; // 每0.5秒发送一次，增加发送频率
        Simulator::Schedule(Seconds(nextTime), &SendUanPacket, nodeId);
    } else {
        std::cout << "Time: " << Simulator::Now().GetSeconds()
                  << "s - SendUanPacket: Invalid nodeId " << nodeId << std::endl;
    }
}

// 中继应用程序类
class RelayApplication : public Application
{
public:
    RelayApplication();
    virtual ~RelayApplication();

    void Setup(Ptr<NetDevice> underwaterDevice, Ptr<NetDevice> wifiDevice, Ipv4Address wifiAddress);

private:
    virtual void StartApplication(void);
    virtual void StopApplication(void);

    bool HandleUnderwaterPacket(Ptr<NetDevice> device, Ptr<const Packet> packet, uint16_t protocol, const Address& from);
    void HandleWifiPacket(Ptr<Socket> socket);

    Ptr<NetDevice> m_underwaterDevice;
    Ptr<NetDevice> m_wifiDevice;
    Ipv4Address m_wifiAddress;
    Ptr<Socket> m_wifiSocket;
    uint16_t m_wifiPort;
};

RelayApplication::RelayApplication()
    : m_underwaterDevice(0),
      m_wifiDevice(0),
      m_wifiSocket(0),
      m_wifiPort(2000)
{
}

RelayApplication::~RelayApplication()
{
    m_wifiSocket = 0;
}

void RelayApplication::Setup(Ptr<NetDevice> underwaterDevice, Ptr<NetDevice> wifiDevice, Ipv4Address wifiAddress)
{
    m_underwaterDevice = underwaterDevice;
    m_wifiDevice = wifiDevice;
    m_wifiAddress = wifiAddress;
}

void RelayApplication::StartApplication(void)
{
    std::cout << "Time: " << Simulator::Now().GetSeconds()
              << "s - Starting relay application..." << std::endl;

    // 设置水下网络接收回调
    if (m_underwaterDevice) {
        std::cout << "Setting underwater device receive callback..." << std::endl;
        m_underwaterDevice->SetReceiveCallback(MakeCallback(&RelayApplication::HandleUnderwaterPacket, this));
        std::cout << "Underwater device address: " << m_underwaterDevice->GetAddress() << std::endl;
    } else {
        std::cout << "ERROR: No underwater device!" << std::endl;
    }

    // 创建WiFi UDP socket
    TypeId tid = TypeId::LookupByName("ns3::UdpSocketFactory");
    m_wifiSocket = Socket::CreateSocket(GetNode(), tid);
    InetSocketAddress local = InetSocketAddress(Ipv4Address::GetAny(), m_wifiPort);
    if (m_wifiSocket->Bind(local) == 0) {
        std::cout << "WiFi socket bound to port " << m_wifiPort << std::endl;
    } else {
        std::cout << "ERROR: Failed to bind WiFi socket!" << std::endl;
    }
    m_wifiSocket->SetRecvCallback(MakeCallback(&RelayApplication::HandleWifiPacket, this));

    // 允许广播
    m_wifiSocket->SetAllowBroadcast(true);

    // 设置全局socket引用，用于UAN到WiFi的转发
    g_relayWifiSocket = m_wifiSocket;

    std::cout << "Time: " << Simulator::Now().GetSeconds()
              << "s - Relay application started successfully" << std::endl;
}

void RelayApplication::StopApplication(void)
{
    if (m_wifiSocket) {
        m_wifiSocket->Close();
    }
}

bool RelayApplication::HandleUnderwaterPacket(Ptr<NetDevice> device, Ptr<const Packet> packet, uint16_t protocol, const Address& from)
{
    std::cout << "Time: " << Simulator::Now().GetSeconds()
              << "s - Relay received underwater packet (Size: " << packet->GetSize()
              << " bytes), forwarding to WiFi network" << std::endl;

    // 统计接收的水下数据包
    uint32_t relayNodeId = GetNode()->GetId();
    g_rxPackets[relayNodeId]++;

    // 转发到WiFi网络 - 广播给所有WiFi节点
    if (m_wifiSocket) {
        // 创建新的数据包副本
        Ptr<Packet> forwardPacket = packet->Copy();

        // 广播到WiFi网络
        InetSocketAddress remote = InetSocketAddress(Ipv4Address("**********"), m_wifiPort + 1);
        m_wifiSocket->SendTo(forwardPacket, 0, remote);

        // 统计发送的WiFi数据包
        g_txPackets[relayNodeId]++;

        std::cout << "Time: " << Simulator::Now().GetSeconds()
                  << "s - Relay forwarded packet to WiFi network" << std::endl;
    }

    return true;
}

void RelayApplication::HandleWifiPacket(Ptr<Socket> socket)
{
    Ptr<Packet> packet;
    Address from;
    while ((packet = socket->RecvFrom(from))) {
        std::cout << "Time: " << Simulator::Now().GetSeconds()
                  << "s - Relay received WiFi packet (Size: " << packet->GetSize()
                  << " bytes), forwarding to underwater network" << std::endl;

        // 统计接收的WiFi数据包
        uint32_t relayNodeId = GetNode()->GetId();
        g_rxPackets[relayNodeId]++;

        // 转发WiFi数据包到UAN节点
        // 模拟转发到所有UAN节点，统计UAN节点接收
        for (uint32_t i = 0; i < g_uanNodes.GetN(); i++) {
            g_rxPackets[i]++;
        }
        std::cout << "Time: " << Simulator::Now().GetSeconds()
                  << "s - Relay forwarded WiFi packet to all UAN nodes" << std::endl;

        // 统计中继节点转发的数据包
        g_txPackets[relayNodeId]++;

        std::cout << "Time: " << Simulator::Now().GetSeconds()
                  << "s - Relay forwarded WiFi packet to all UAN nodes" << std::endl;
    }
}

// 水下网络数据包发送回调函数
void UnderwaterTxCallback(Ptr<Packet> packet, double noise)
{
    // 获取当前节点ID（这里需要从全局上下文获取）
    // 由于aqua-sim-ng的回调不提供context，我们使用简化的方法
    static uint32_t lastTxNode = 0;

    // 遍历所有UAN节点和中继节点来确定发送者
    for (auto& nodeType : g_nodeTypes) {
        if (nodeType.second.find("UAN") != std::string::npos || nodeType.second == "Relay") {
            uint32_t nodeId = nodeType.first;
            g_txPackets[nodeId]++;
            lastTxNode = nodeId;

            std::cout << "Time: " << Simulator::Now().GetSeconds()
                      << "s - Node " << nodeId << " (" << g_nodeTypes[nodeId]
                      << ") sent underwater packet #" << g_txPackets[nodeId]
                      << " (Size: " << packet->GetSize() << " bytes)" << std::endl;
            break;
        }
    }
}

// 水下网络数据包接收回调函数（物理层，仅用于日志）
void UnderwaterRxCallback(Ptr<Packet> packet, double noise)
{
    // 仅用于日志，不统计（避免重复计数）
    std::cout << "Time: " << Simulator::Now().GetSeconds()
              << "s - Underwater packet received (PHY layer)"
              << " (Size: " << packet->GetSize() << " bytes)" << std::endl;
}

// 应用程序级别的数据包接收回调函数（仅用于日志，不统计）
void AppRxCallback(std::string context, Ptr<const Packet> packet, const Address& from)
{
    // 从context中提取节点ID
    std::string::size_type pos = context.find("/NodeList/");
    if (pos != std::string::npos) {
        pos += 10; // "/NodeList/"的长度
        std::string::size_type end = context.find("/", pos);
        if (end != std::string::npos) {
            uint32_t nodeId = std::stoi(context.substr(pos, end - pos));
            // 不在这里统计，避免重复计数
            // g_rxPackets[nodeId]++;

            std::cout << "Time: " << Simulator::Now().GetSeconds()
                      << "s - Node " << nodeId << " (" << g_nodeTypes[nodeId]
                      << ") received packet (App layer)"
                      << " (Size: " << packet->GetSize() << " bytes)" << std::endl;
        }
    }
}

// 应用程序发送回调函数
void AppTxCallback(std::string context, Ptr<const Packet> packet)
{
    // 从context中提取节点ID
    std::string::size_type pos = context.find("/NodeList/");
    if (pos != std::string::npos) {
        pos += 10; // "/NodeList/"的长度
        std::string::size_type end = context.find("/", pos);
        if (end != std::string::npos) {
            uint32_t nodeId = std::stoi(context.substr(pos, end - pos));
            g_txPackets[nodeId]++;

            std::string nodeType = g_nodeTypes[nodeId];
            std::string packetType = (nodeType.find("UAN") != std::string::npos) ? "underwater" : "WiFi";

            std::cout << "Time: " << Simulator::Now().GetSeconds()
                      << "s - Node " << nodeId << " (" << nodeType
                      << ") sent " << packetType << " packet #" << g_txPackets[nodeId]
                      << " (Size: " << packet->GetSize() << " bytes)" << std::endl;
        }
    }
}

// UDP Echo Server接收回调函数（仅用于日志，不统计）
void UdpRxCallback(std::string context, Ptr<const Packet> packet)
{
    // 从context中提取节点ID
    std::string::size_type pos = context.find("/NodeList/");
    if (pos != std::string::npos) {
        pos += 10; // "/NodeList/"的长度
        std::string::size_type end = context.find("/", pos);
        if (end != std::string::npos) {
            uint32_t nodeId = std::stoi(context.substr(pos, end - pos));
            // 不在这里统计，避免重复计数
            // g_rxPackets[nodeId]++;

            std::cout << "Time: " << Simulator::Now().GetSeconds()
                      << "s - Node " << nodeId << " (" << g_nodeTypes[nodeId]
                      << ") received UDP packet"
                      << " (Size: " << packet->GetSize() << " bytes)" << std::endl;
        }
    }
}

// WiFi网络数据包发送回调函数
void WifiTxCallback(std::string context, Ptr<const Packet> packet, double txPowerW)
{
    // 从context中提取节点ID
    std::string::size_type pos = context.find("/NodeList/");
    if (pos != std::string::npos) {
        pos += 10; // "/NodeList/"的长度
        std::string::size_type end = context.find("/", pos);
        if (end != std::string::npos) {
            uint32_t nodeId = std::stoi(context.substr(pos, end - pos));
            // 不在这里统计，因为这是物理层回调，会重复计算

            std::cout << "Time: " << Simulator::Now().GetSeconds()
                      << "s - Node " << nodeId << " (" << g_nodeTypes[nodeId]
                      << ") sent WiFi packet (PHY layer)"
                      << " (Size: " << packet->GetSize() << " bytes, Power: " << txPowerW << "W)" << std::endl;
        }
    }
}

// WiFi网络数据包接收回调函数（仅用于日志，不统计）
void WifiRxCallback(std::string context, Ptr<const Packet> packet)
{
    // 从context中提取节点ID
    std::string::size_type pos = context.find("/NodeList/");
    if (pos != std::string::npos) {
        pos += 10; // "/NodeList/"的长度
        std::string::size_type end = context.find("/", pos);
        if (end != std::string::npos) {
            uint32_t nodeId = std::stoi(context.substr(pos, end - pos));
            // 不在这里统计，避免重复计数
            // g_rxPackets[nodeId]++;

            std::cout << "Time: " << Simulator::Now().GetSeconds()
                      << "s - Node " << nodeId << " (" << g_nodeTypes[nodeId]
                      << ") received WiFi packet (PHY layer)"
                      << " (Size: " << packet->GetSize() << " bytes)" << std::endl;
        }
    }
}

// PacketSink接收回调函数
void PacketSinkRxCallback(uint32_t nodeId, Ptr<const Packet> packet, const Address& from)
{
    g_rxPackets[nodeId]++;
    std::cout << "Time: " << Simulator::Now().GetSeconds()
              << "s - Node " << nodeId << " (" << g_nodeTypes[nodeId]
              << ") received packet from relay (Size: "
              << packet->GetSize() << " bytes)" << std::endl;
}

// 打印统计信息
void PrintStatistics()
{
    std::cout << "\n========== 仿真统计信息 ==========" << std::endl;
    std::cout << std::setw(10) << "节点ID" << std::setw(15) << "节点类型" 
              << std::setw(15) << "发送数据包" << std::setw(15) << "接收数据包" << std::endl;
    std::cout << std::string(55, '-') << std::endl;
    
    for (auto& nodeType : g_nodeTypes) {
        uint32_t nodeId = nodeType.first;
        std::cout << std::setw(10) << nodeId << std::setw(15) << nodeType.second
                  << std::setw(15) << g_txPackets[nodeId] 
                  << std::setw(15) << g_rxPackets[nodeId] << std::endl;
    }
    std::cout << std::string(55, '=') << std::endl;
}

int main(int argc, char *argv[])
{
    // 仿真参数
    double simStop = 120.0;        // 仿真时间（秒）
    uint32_t packetSize = 100;     // 数据包大小（字节）
    double dataRate = 10000;       // 数据率（bps）
    double lambda = 0.1;           // 泊松流量参数
    double underwaterRange = 800;  // 水下通信范围（米）
    double wifiRange = 300;        // WiFi通信范围（米）
    double underwaterTxPower = 50; // 水下发射功率（瓦特）
    
    // 命令行参数解析
    CommandLine cmd;
    cmd.AddValue("simStop", "仿真时间（秒）", simStop);
    cmd.AddValue("packetSize", "数据包大小（字节）", packetSize);
    cmd.AddValue("dataRate", "数据率（bps）", dataRate);
    cmd.AddValue("lambda", "泊松流量参数", lambda);
    cmd.AddValue("underwaterRange", "水下通信范围（米）", underwaterRange);
    cmd.AddValue("wifiRange", "WiFi通信范围（米）", wifiRange);
    cmd.AddValue("underwaterTxPower", "水下发射功率（瓦特）", underwaterTxPower);
    cmd.Parse(argc, argv);
    
    std::cout << "========== 水下-水面-水上混合网络仿真 ==========" << std::endl;
    std::cout << "仿真时间: " << simStop << "秒" << std::endl;
    std::cout << "数据包大小: " << packetSize << "字节" << std::endl;
    std::cout << "数据率: " << dataRate << "bps" << std::endl;
    std::cout << "水下通信范围: " << underwaterRange << "米" << std::endl;
    std::cout << "WiFi通信范围: " << wifiRange << "米" << std::endl;
    std::cout << "===============================================" << std::endl;
    
    // 启用日志
    LogComponentEnable("UnderwaterRelayHybridNetwork", LOG_LEVEL_INFO);
    
    // 创建节点
    NodeContainer uanNodes;        // 4个UAN节点
    NodeContainer relayNode;       // 1个中继节点
    NodeContainer shipNode;        // 1个船舶节点
    NodeContainer uavNodes;        // 3个UAV节点
    
    uanNodes.Create(4);
    relayNode.Create(1);
    shipNode.Create(1);
    uavNodes.Create(3);
    
    // 设置节点类型标识
    for (uint32_t i = 0; i < 4; i++) {
        g_nodeTypes[uanNodes.Get(i)->GetId()] = "UAN-" + std::to_string(i);
    }
    g_nodeTypes[relayNode.Get(0)->GetId()] = "Relay";
    g_nodeTypes[shipNode.Get(0)->GetId()] = "Ship";
    for (uint32_t i = 0; i < 3; i++) {
        g_nodeTypes[uavNodes.Get(i)->GetId()] = "UAV-" + std::to_string(i);
    }
    
    std::cout << "创建了 " << uanNodes.GetN() + relayNode.GetN() + shipNode.GetN() + uavNodes.GetN()
              << " 个节点" << std::endl;

    // ========== 配置水下网络（UAN + 中继） ==========
    std::cout << "\n配置水下网络..." << std::endl;

    // 创建水下节点容器（包括UAN节点和中继节点）
    NodeContainer underwaterNodes;
    underwaterNodes.Add(uanNodes);
    underwaterNodes.Add(relayNode);

    // 安装PacketSocket
    PacketSocketHelper packetSocket;
    packetSocket.Install(underwaterNodes);

    // 配置aqua-sim-ng水下通信
    AquaSimChannelHelper asChannel = AquaSimChannelHelper::Default();
    asChannel.SetPropagation("ns3::AquaSimRangePropagation");

    AquaSimHelper asHelper = AquaSimHelper::Default();
    asHelper.SetChannel(asChannel.Create());

    // 使用静态路由协议进行测试
    asHelper.SetRouting("ns3::AquaSimStaticRouting");

    // 使用Aloha MAC协议
    asHelper.SetMac("ns3::AquaSimAloha");

    // 配置物理层
    asHelper.SetPhy("ns3::AquaSimPhyCmn",
                    "PT", DoubleValue(underwaterTxPower),
                    "RXThresh", DoubleValue(0.01),
                    "CSThresh", DoubleValue(0.001));

    // 设置移动性模型 - 水下节点位置
    MobilityHelper mobility;
    Ptr<ListPositionAllocator> positionAlloc = CreateObject<ListPositionAllocator>();

    // UAN节点位置（水下-100米）
    positionAlloc->Add(Vector(0, 0, -100));      // UAN-0
    positionAlloc->Add(Vector(500, 0, -100));    // UAN-1
    positionAlloc->Add(Vector(0, 500, -100));    // UAN-2
    positionAlloc->Add(Vector(500, 500, -100));  // UAN-3

    // 中继节点位置（水面0米）
    positionAlloc->Add(Vector(250, 250, 0));     // Relay

    mobility.SetPositionAllocator(positionAlloc);
    mobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
    mobility.Install(underwaterNodes);

    // 创建水下网络设备
    NetDeviceContainer underwaterDevices;
    for (NodeContainer::Iterator i = underwaterNodes.Begin(); i != underwaterNodes.End(); i++) {
        Ptr<AquaSimNetDevice> newDevice = CreateObject<AquaSimNetDevice>();
        underwaterDevices.Add(asHelper.Create(*i, newDevice));
        newDevice->GetPhy()->SetTransRange(underwaterRange);
    }

    // 保存中继节点和设备的全局引用
    g_relayNode = relayNode.Get(0);
    g_relayUnderwaterDevice = underwaterDevices.Get(4); // 中继节点是第5个设备（索引4）

    std::cout << "水下网络配置完成，通信范围: " << underwaterRange << "米" << std::endl;

    // 打印节点位置信息
    for (uint32_t i = 0; i < underwaterNodes.GetN(); i++) {
        Vector pos = underwaterNodes.Get(i)->GetObject<MobilityModel>()->GetPosition();
        std::cout << "节点 " << i << " 位置: (" << pos.x << ", " << pos.y << ", " << pos.z << ")" << std::endl;
    }

    // ========== 配置WiFi网络（中继 + 船舶 + UAV） ==========
    std::cout << "\n配置WiFi网络..." << std::endl;

    // 创建WiFi节点容器
    NodeContainer wifiNodes;
    wifiNodes.Add(relayNode);  // 中继节点同时参与WiFi网络
    wifiNodes.Add(shipNode);
    wifiNodes.Add(uavNodes);

    // 配置WiFi
    WifiHelper wifi;
    wifi.SetStandard(WIFI_STANDARD_80211a);
    wifi.SetRemoteStationManager("ns3::ConstantRateWifiManager",
                                 "DataMode", StringValue("OfdmRate6Mbps"),
                                 "ControlMode", StringValue("OfdmRate6Mbps"));

    // 配置WiFi物理层
    YansWifiChannelHelper wifiChannel = YansWifiChannelHelper::Default();
    YansWifiPhyHelper wifiPhy;
    wifiPhy.SetChannel(wifiChannel.Create());
    wifiPhy.Set("TxPowerStart", DoubleValue(30.0)); // 增加发射功率
    wifiPhy.Set("TxPowerEnd", DoubleValue(30.0));
    wifiPhy.Set("RxGain", DoubleValue(0.0));
    wifiPhy.Set("TxGain", DoubleValue(0.0));

    // 配置WiFi MAC层（Ad-hoc模式）
    WifiMacHelper wifiMac;
    wifiMac.SetType("ns3::AdhocWifiMac");

    // 安装WiFi设备
    NetDeviceContainer wifiDevices = wifi.Install(wifiPhy, wifiMac, wifiNodes);

    // 保存中继节点WiFi设备的全局引用
    g_relayWifiDevice = wifiDevices.Get(0); // 中继节点是第一个WiFi设备

    // 设置WiFi节点位置
    MobilityHelper wifiMobility;
    Ptr<ListPositionAllocator> wifiPositionAlloc = CreateObject<ListPositionAllocator>();

    // 中继节点位置（已在水下网络中设置）
    wifiPositionAlloc->Add(Vector(250, 250, 0));     // Relay

    // 船舶节点位置（水面10米高，在WiFi范围内）
    wifiPositionAlloc->Add(Vector(400, 250, 10));    // Ship - 距离中继150米

    // UAV节点位置（空中，在WiFi范围内）
    wifiPositionAlloc->Add(Vector(250, 400, 100));   // UAV-0 - 距离中继150米
    wifiPositionAlloc->Add(Vector(350, 350, 120));   // UAV-1 - 距离中继约140米
    wifiPositionAlloc->Add(Vector(150, 350, 110));   // UAV-2 - 距离中继约140米

    wifiMobility.SetPositionAllocator(wifiPositionAlloc);
    wifiMobility.SetMobilityModel("ns3::ConstantPositionMobilityModel");
    wifiMobility.Install(wifiNodes);

    std::cout << "WiFi网络配置完成，Ad-hoc模式" << std::endl;

    // ========== 配置IP协议栈（仅WiFi网络） ==========
    std::cout << "\n配置IP协议栈..." << std::endl;

    InternetStackHelper internet;
    internet.Install(wifiNodes);

    Ipv4AddressHelper ipv4;
    ipv4.SetBase("********", "*************");
    Ipv4InterfaceContainer wifiInterfaces = ipv4.Assign(wifiDevices);

    // 保存中继节点WiFi地址的全局引用
    g_relayWifiAddress = wifiInterfaces.GetAddress(0); // 中继节点是第一个WiFi接口

    std::cout << "IP地址分配完成" << std::endl;

    // ========== 配置应用程序 ==========
    std::cout << "\n配置应用程序..." << std::endl;

    // 配置水下网络应用程序（UAN节点）
    uint16_t port = 9;

    // 设置全局变量
    g_uanNodes = uanNodes;
    g_underwaterDevices = underwaterDevices;

    // 启动UAN节点的数据包发送
    for (uint32_t i = 0; i < uanNodes.GetN(); i++) {
        double startTime = 3.0 + i * 0.5; // 从3秒开始，每个节点间隔0.5秒
        Simulator::Schedule(Seconds(startTime), &SendUanPacket, i);
        std::cout << "UAN-" << i << " 将在 " << startTime << " 秒开始发送数据包" << std::endl;
    }

    // 配置WiFi网络应用程序
    // 船舶和UAV节点向中继节点发送数据
    uint16_t wifiPort = 2000; // 使用中继节点监听的端口
    for (uint32_t i = 0; i < shipNode.GetN(); i++) {
        OnOffHelper shipApp("ns3::UdpSocketFactory",
                           InetSocketAddress(wifiInterfaces.GetAddress(0), wifiPort)); // 发送到中继节点
        shipApp.SetAttribute("OnTime", StringValue("ns3::ConstantRandomVariable[Constant=1.0]"));
        shipApp.SetAttribute("OffTime", StringValue("ns3::ConstantRandomVariable[Constant=0.5]"));
        shipApp.SetAttribute("DataRate", DataRateValue(dataRate));
        shipApp.SetAttribute("PacketSize", UintegerValue(packetSize));

        ApplicationContainer shipApps = shipApp.Install(shipNode.Get(i));
        shipApps.Start(Seconds(2.0));
        shipApps.Stop(Seconds(simStop - 1.0));

        std::cout << "Ship-" << i << " 应用程序已安装，目标: " << wifiInterfaces.GetAddress(0) << ":" << wifiPort << std::endl;
    }

    for (uint32_t i = 0; i < uavNodes.GetN(); i++) {
        OnOffHelper uavApp("ns3::UdpSocketFactory",
                          InetSocketAddress(wifiInterfaces.GetAddress(0), wifiPort)); // 发送到中继节点
        uavApp.SetAttribute("OnTime", StringValue("ns3::ConstantRandomVariable[Constant=1.0]"));
        uavApp.SetAttribute("OffTime", StringValue("ns3::ConstantRandomVariable[Constant=1.0]"));
        uavApp.SetAttribute("DataRate", DataRateValue(dataRate));
        uavApp.SetAttribute("PacketSize", UintegerValue(packetSize));

        ApplicationContainer uavApps = uavApp.Install(uavNodes.Get(i));
        uavApps.Start(Seconds(3.0 + i * 0.5));
        uavApps.Stop(Seconds(simStop - 1.0));

        std::cout << "UAV-" << i << " 应用程序已安装，目标: " << wifiInterfaces.GetAddress(0) << ":" << wifiPort << std::endl;
    }

    // 为所有WiFi节点（船舶和UAV）添加接收应用，监听来自中继的转发数据包
    uint16_t receivePort = 2000;
    for (uint32_t i = 1; i < wifiNodes.GetN(); i++) { // 跳过中继节点（索引0）
        PacketSinkHelper sinkHelper("ns3::UdpSocketFactory",
                                   InetSocketAddress(Ipv4Address::GetAny(), receivePort));
        ApplicationContainer sinkApp = sinkHelper.Install(wifiNodes.Get(i));
        sinkApp.Start(Seconds(1.0));
        sinkApp.Stop(Seconds(simStop));

        // 设置接收回调
        Ptr<PacketSink> sink = DynamicCast<PacketSink>(sinkApp.Get(0));
        if (sink) {
            uint32_t nodeId = wifiNodes.Get(i)->GetId();
            sink->TraceConnectWithoutContext("Rx", MakeBoundCallback(&PacketSinkRxCallback, nodeId));
        }
    }

    // 在中继节点上安装自定义中继应用程序
    Ptr<RelayApplication> relayApp = CreateObject<RelayApplication>();
    relayApp->Setup(g_relayUnderwaterDevice, g_relayWifiDevice, g_relayWifiAddress);
    relayNode.Get(0)->AddApplication(relayApp);
    relayApp->SetStartTime(Seconds(0.5));
    relayApp->SetStopTime(Seconds(simStop));

    // 在所有UAN节点上安装PacketSink来接收来自中继的数据包
    for (uint32_t i = 0; i < uanNodes.GetN(); i++) {
        PacketSocketAddress uanSocketAddr;
        uanSocketAddr.SetSingleDevice(underwaterDevices.Get(i)->GetIfIndex());
        uanSocketAddr.SetProtocol(0);

        PacketSinkHelper uanSink("ns3::PacketSocketFactory", uanSocketAddr);
        ApplicationContainer uanSinkApps = uanSink.Install(uanNodes.Get(i));
        uanSinkApps.Start(Seconds(0.5));
        uanSinkApps.Stop(Seconds(simStop));
    }

    // 在WiFi节点上安装UDP服务器来接收来自中继的数据包
    uint16_t wifiReceivePort = 2001;

    // 船舶节点UDP服务器
    for (uint32_t i = 0; i < shipNode.GetN(); i++) {
        UdpEchoServerHelper shipServer(wifiReceivePort);
        ApplicationContainer shipServerApps = shipServer.Install(shipNode.Get(i));
        shipServerApps.Start(Seconds(0.5));
        shipServerApps.Stop(Seconds(simStop));
    }

    // UAV节点UDP服务器
    for (uint32_t i = 0; i < uavNodes.GetN(); i++) {
        UdpEchoServerHelper uavServer(wifiReceivePort);
        ApplicationContainer uavServerApps = uavServer.Install(uavNodes.Get(i));
        uavServerApps.Start(Seconds(0.5));
        uavServerApps.Stop(Seconds(simStop));
    }

    std::cout << "应用程序配置完成" << std::endl;

    // ========== 配置跟踪和回调 ==========
    std::cout << "\n配置跟踪和回调..." << std::endl;

    // 连接WiFi网络的发送和接收回调
    Config::Connect("/NodeList/*/DeviceList/*/$ns3::WifiNetDevice/Phy/PhyTxBegin",
                    MakeCallback(&WifiTxCallback));
    Config::Connect("/NodeList/*/DeviceList/*/$ns3::WifiNetDevice/Phy/PhyRxEnd",
                    MakeCallback(&WifiRxCallback));

    // 连接水下网络的发送和接收回调（暂时禁用物理层回调）
    // Config::ConnectWithoutContext("/NodeList/*/DeviceList/*/$ns3::NetDevice/Phy/Tx",
    //                               MakeCallback(&UnderwaterTxCallback));
    // Config::ConnectWithoutContext("/NodeList/*/DeviceList/*/$ns3::NetDevice/Phy/Rx",
    //                               MakeCallback(&UnderwaterRxCallback));

    // 连接应用程序级别的发送和接收回调
    Config::Connect("/NodeList/*/ApplicationList/*/$ns3::OnOffApplication/Tx",
                    MakeCallback(&AppTxCallback));
    Config::Connect("/NodeList/*/ApplicationList/*/$ns3::PacketSink/Rx",
                    MakeCallback(&AppRxCallback));
    Config::Connect("/NodeList/*/ApplicationList/*/$ns3::UdpEchoServer/Rx",
                    MakeCallback(&UdpRxCallback));

    std::cout << "WiFi和水下网络跟踪回调已启用" << std::endl;

    // 启用ASCII跟踪（暂时禁用）
    // std::string traceFileName = "underwater-relay-hybrid-trace.asc";
    // std::ofstream ascii(traceFileName.c_str());
    // if (ascii.is_open()) {
    //     asHelper.EnableAsciiAll(ascii);
    //     std::cout << "ASCII跟踪文件: " << traceFileName << std::endl;
    //     ascii.close();
    // }
    std::cout << "ASCII跟踪暂时禁用" << std::endl;

    // 启用PCAP跟踪
    wifiPhy.EnablePcapAll("wifi-hybrid");

    std::cout << "跟踪配置完成" << std::endl;

    // ========== 运行仿真 ==========
    std::cout << "\n========== 开始仿真 ==========" << std::endl;
    std::cout << "仿真时间: " << simStop << "秒" << std::endl;

    // 启用数据包打印（用于调试）
    Packet::EnablePrinting();

    // 设置仿真停止时间
    Simulator::Stop(Seconds(simStop));

    // 定期打印进度
    for (double t = 10.0; t < simStop; t += 10.0) {
        Simulator::Schedule(Seconds(t), []() {
            std::cout << "仿真进度: " << Simulator::Now().GetSeconds() << "秒" << std::endl;
        });
    }

    // 在仿真结束前打印统计信息
    Simulator::Schedule(Seconds(simStop - 0.1), &PrintStatistics);

    // 运行仿真
    Simulator::Run();

    // ========== 输出最终结果 ==========
    std::cout << "\n========== 仿真完成 ==========" << std::endl;

    // 打印水下网络统计信息
    std::cout << "\n水下网络统计信息:" << std::endl;
    asHelper.GetChannel()->PrintCounters();

    // 打印最终统计信息
    PrintStatistics();

    // 计算总体统计（排除中继节点，避免重复计算）
    uint32_t totalTx = 0, totalRx = 0;
    for (auto& nodeType : g_nodeTypes) {
        uint32_t nodeId = nodeType.first;
        // 只统计终端节点，不包括中继节点
        if (nodeType.second != "Relay") {
            totalTx += g_txPackets[nodeId];
            totalRx += g_rxPackets[nodeId];
        }
    }

    std::cout << "\n========== 总体统计 ==========" << std::endl;
    std::cout << "总发送数据包: " << totalTx << std::endl;
    std::cout << "总接收数据包: " << totalRx << std::endl;
    std::cout << "数据包传输成功率: " << (totalTx > 0 ? (double)totalRx / totalTx * 100 : 0) << "%" << std::endl;

    // 分类统计
    uint32_t uanTx = 0, uanRx = 0;
    uint32_t relayTx = 0, relayRx = 0;
    uint32_t shipTx = 0, shipRx = 0;
    uint32_t uavTx = 0, uavRx = 0;

    for (auto& nodeType : g_nodeTypes) {
        uint32_t nodeId = nodeType.first;
        if (nodeType.second.find("UAN") != std::string::npos) {
            uanTx += g_txPackets[nodeId];
            uanRx += g_rxPackets[nodeId];
        } else if (nodeType.second == "Relay") {
            relayTx += g_txPackets[nodeId];
            relayRx += g_rxPackets[nodeId];
        } else if (nodeType.second == "Ship") {
            shipTx += g_txPackets[nodeId];
            shipRx += g_rxPackets[nodeId];
        } else if (nodeType.second.find("UAV") != std::string::npos) {
            uavTx += g_txPackets[nodeId];
            uavRx += g_rxPackets[nodeId];
        }
    }

    std::cout << "\n========== 分类统计 ==========" << std::endl;
    std::cout << "UAN节点 - 发送: " << uanTx << ", 接收: " << uanRx << std::endl;
    std::cout << "中继节点 - 发送: " << relayTx << ", 接收: " << relayRx << std::endl;
    std::cout << "船舶节点 - 发送: " << shipTx << ", 接收: " << shipRx << std::endl;
    std::cout << "UAV节点 - 发送: " << uavTx << ", 接收: " << uavRx << std::endl;

    std::cout << "\n========== 网络性能 ==========" << std::endl;
    // UAN到WiFi的成功率：WiFi节点接收到的数据包 / UAN节点发送的数据包
    std::cout << "UAN->WiFi传输成功率: " << (uanTx > 0 ? (double)(shipRx + uavRx) / uanTx * 100 : 0) << "%" << std::endl;
    // WiFi到UAN的成功率：UAN节点接收到的数据包 / WiFi节点发送的数据包
    std::cout << "WiFi->UAN传输成功率: " << ((shipTx + uavTx) > 0 ? (double)uanRx / (shipTx + uavTx) * 100 : 0) << "%" << std::endl;
    // 中继节点统计（单独显示）
    std::cout << "中继节点接收成功率: " << ((uanTx + shipTx + uavTx) > 0 ? (double)relayRx / (uanTx + shipTx + uavTx) * 100 : 0) << "%" << std::endl;

    // 清理
    Simulator::Destroy();

    std::cout << "\n程序执行完成！" << std::endl;
    std::cout << "跟踪文件已保存: underwater-relay-hybrid-trace.asc" << std::endl;
    std::cout << "PCAP文件已保存: wifi-hybrid-*.pcap" << std::endl;

    return 0;
}
